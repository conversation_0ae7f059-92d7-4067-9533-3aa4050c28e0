import { Post } from '../models/Post.js';
import { Pet } from '../models/Pet.js';
import { 
  successResponse, 
  errorResponse, 
  notFoundResponse,
  forbiddenResponse,
  paginatedResponse,
  validationErrorResponse 
} from '../utils/response.js';

/**
 * 创建寻宠帖子
 */
export async function createPost(req, res) {
  try {
    const userId = req.user.id;
    const { pet_id } = req.body;

    // 检查宠物是否存在且属于当前用户
    const pet = await Pet.findById(pet_id);
    if (!pet) {
      return notFoundResponse(res, '宠物不存在');
    }

    const belongsToUser = await Pet.belongsToUser(pet_id, userId);
    if (!belongsToUser) {
      return forbiddenResponse(res, '您没有权限为此宠物创建寻宠帖子');
    }

    const postData = { ...req.body, user_id: userId };
    const newPost = await Post.create(postData);

    return successResponse(res, newPost.toJSON(), '寻宠帖子创建成功，等待管理员审核', 201);

  } catch (error) {
    console.error('创建帖子失败:', error);
    return errorResponse(res, '创建寻宠帖子失败，请稍后重试', 500);
  }
}

/**
 * 获取帖子列表（公开，已审核通过的）
 */
export async function getPosts(req, res) {
  try {
    const {
      species,
      color,
      gender,
      location,
      keyword,
      dateFrom,
      dateTo,
      post_status = 'searching'
    } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const filters = {
      admin_status: 'approved', // 只显示已审核通过的帖子
      post_status,
      species,
      color,
      gender,
      location,
      keyword,
      dateFrom,
      dateTo
    };

    // 移除空值，避免传递空字符串给数据库
    Object.keys(filters).forEach(key => {
      if (!filters[key]) {
        delete filters[key];
      }
    });

    const posts = await Post.findAll(filters, { page, limit });
    const total = await Post.count(filters);

    return paginatedResponse(res, posts.map(post => post.toJSON()), {
      page,
      limit,
      total
    }, '获取帖子列表成功');

  } catch (error) {
    console.error('获取帖子列表失败:', error);
    return errorResponse(res, '获取帖子列表失败，请稍后重试', 500);
  }
}

/**
 * 获取用户的帖子列表
 */
export async function getUserPosts(req, res) {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const filters = { user_id: userId };
    const posts = await Post.findAll(filters, { page, limit });
    const total = await Post.count(filters);

    return paginatedResponse(res, posts.map(post => post.toJSON()), {
      page,
      limit,
      total
    }, '获取我的帖子列表成功');

  } catch (error) {
    console.error('获取用户帖子列表失败:', error);
    return errorResponse(res, '获取帖子列表失败，请稍后重试', 500);
  }
}

/**
 * 根据ID获取帖子详情
 */
export async function getPostById(req, res) {
  try {
    const postId = parseInt(req.params.id);
    
    if (isNaN(postId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的帖子ID' }]);
    }

    const post = await Post.findById(postId);
    
    if (!post) {
      return notFoundResponse(res, '帖子不存在');
    }

    // 如果帖子未审核通过，只有帖子作者和管理员可以查看
    if (post.admin_status !== 'approved') {
      if (!req.user || (req.user.id !== post.user_id && !req.user.isAdmin)) {
        return forbiddenResponse(res, '该帖子正在审核中');
      }
    }

    return successResponse(res, post.toJSON(), '获取帖子详情成功');

  } catch (error) {
    console.error('获取帖子详情失败:', error);
    return errorResponse(res, '获取帖子详情失败，请稍后重试', 500);
  }
}

/**
 * 更新帖子信息
 */
export async function updatePost(req, res) {
  try {
    const postId = parseInt(req.params.id);
    const userId = req.user.id;
    
    if (isNaN(postId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的帖子ID' }]);
    }

    // 检查帖子是否存在
    const existingPost = await Post.findById(postId);
    if (!existingPost) {
      return notFoundResponse(res, '帖子不存在');
    }

    // 检查帖子是否属于当前用户
    const belongsToUser = await Post.belongsToUser(postId, userId);
    if (!belongsToUser) {
      return forbiddenResponse(res, '您没有权限修改此帖子');
    }

    const updatedPost = await Post.update(postId, req.body);

    return successResponse(res, updatedPost.toJSON(), '帖子更新成功');

  } catch (error) {
    console.error('更新帖子失败:', error);
    return errorResponse(res, '更新帖子失败，请稍后重试', 500);
  }
}

/**
 * 更新帖子状态（找到/关闭）
 */
export async function updatePostStatus(req, res) {
  try {
    const postId = parseInt(req.params.id);
    const userId = req.user.id;
    const { post_status } = req.body;
    
    if (isNaN(postId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的帖子ID' }]);
    }

    // 检查帖子是否存在
    const existingPost = await Post.findById(postId);
    if (!existingPost) {
      return notFoundResponse(res, '帖子不存在');
    }

    // 检查帖子是否属于当前用户
    const belongsToUser = await Post.belongsToUser(postId, userId);
    if (!belongsToUser) {
      return forbiddenResponse(res, '您没有权限修改此帖子状态');
    }

    const updatedPost = await Post.update(postId, { post_status });

    let message = '帖子状态更新成功';
    if (post_status === 'found') {
      message = '恭喜！已标记为找到宠物';
    } else if (post_status === 'closed') {
      message = '帖子已关闭';
    }

    return successResponse(res, updatedPost.toJSON(), message);

  } catch (error) {
    console.error('更新帖子状态失败:', error);
    return errorResponse(res, '更新帖子状态失败，请稍后重试', 500);
  }
}

/**
 * 删除帖子
 */
export async function deletePost(req, res) {
  try {
    const postId = parseInt(req.params.id);
    const userId = req.user.id;
    
    if (isNaN(postId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的帖子ID' }]);
    }

    // 检查帖子是否存在
    const existingPost = await Post.findById(postId);
    if (!existingPost) {
      return notFoundResponse(res, '帖子不存在');
    }

    // 检查帖子是否属于当前用户
    const belongsToUser = await Post.belongsToUser(postId, userId);
    if (!belongsToUser) {
      return forbiddenResponse(res, '您没有权限删除此帖子');
    }

    const success = await Post.delete(postId);
    
    if (!success) {
      return errorResponse(res, '删除帖子失败', 500);
    }

    return successResponse(res, null, '帖子删除成功');

  } catch (error) {
    console.error('删除帖子失败:', error);
    return errorResponse(res, '删除帖子失败，请稍后重试', 500);
  }
}

/**
 * 管理员审核帖子
 */
export async function reviewPost(req, res) {
  try {
    const postId = parseInt(req.params.id);
    const { admin_status, reason } = req.body;
    
    if (isNaN(postId)) {
      return validationErrorResponse(res, [{ field: 'id', message: '无效的帖子ID' }]);
    }

    // 检查帖子是否存在
    const existingPost = await Post.findById(postId);
    if (!existingPost) {
      return notFoundResponse(res, '帖子不存在');
    }

    const updatedPost = await Post.updateAdminStatus(postId, admin_status);

    let message = '帖子审核状态更新成功';
    if (admin_status === 'approved') {
      message = '帖子审核通过';
    } else if (admin_status === 'rejected') {
      message = '帖子审核被拒绝';
    }

    // TODO: 发送通知给帖子作者
    // 如果被拒绝，可以包含拒绝理由

    return successResponse(res, updatedPost.toJSON(), message);

  } catch (error) {
    console.error('审核帖子失败:', error);
    return errorResponse(res, '审核帖子失败，请稍后重试', 500);
  }
}

/**
 * 获取待审核帖子列表（管理员用）
 */
export async function getPendingPosts(req, res) {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const filters = { admin_status: 'pending' };
    const posts = await Post.findAll(filters, { page, limit });
    const total = await Post.count(filters);

    return paginatedResponse(res, posts.map(post => post.toJSON()), {
      page,
      limit,
      total
    }, '获取待审核帖子列表成功');

  } catch (error) {
    console.error('获取待审核帖子失败:', error);
    return errorResponse(res, '获取待审核帖子失败，请稍后重试', 500);
  }
}

/**
 * 获取所有帖子列表（管理员用，不限制帖子状态）
 */
export async function getAllPostsForAdmin(req, res) {
  try {
    const {
      species,
      color,
      location,
      post_status,
      admin_status
    } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const filters = {
      post_status,
      admin_status,
      species,
      color,
      location
    };

    // 移除空值
    Object.keys(filters).forEach(key => {
      if (!filters[key]) {
        delete filters[key];
      }
    });

    const posts = await Post.findAll(filters, { page, limit });
    const total = await Post.count(filters);

    return paginatedResponse(res, posts.map(post => post.toJSON()), {
      page,
      limit,
      total
    }, '获取帖子列表成功');

  } catch (error) {
    console.error('获取帖子列表失败:', error);
    return errorResponse(res, '获取帖子列表失败，请稍后重试', 500);
  }
}

/**
 * 获取帖子统计信息（管理员用）
 */
export async function getPostStats(req, res) {
  try {
    const stats = await Post.getStats();

    return successResponse(res, stats, '获取帖子统计成功');

  } catch (error) {
    console.error('获取帖子统计失败:', error);
    return errorResponse(res, '获取帖子统计失败，请稍后重试', 500);
  }
}
